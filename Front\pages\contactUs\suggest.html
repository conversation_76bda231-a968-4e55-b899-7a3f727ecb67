<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>电源网</title>
  <!-- 第三方css -->
  <link rel="stylesheet" href="../../modules/iconfont/iconfont.css">
  <link rel="stylesheet" href="../../modules/layui/css/layui.css">
  <!-- 第三方库js -->
  <script src="../../modules/jq.js"></script>
  <script src="../../modules/layui/layui.js"></script>
  <script src="../../modules/xm-select/xm-select.js"></script>
  <!-- 基础css -->
  <link rel="stylesheet" href="../../css/public.css">
  <link rel="stylesheet" href="../../css/header.css">
  <link rel="stylesheet" href="../../css/footer.css">
  <link rel="stylesheet" href="../../css/header.css">
  <link rel="stylesheet" href="../../css/media.css">
  <link rel="stylesheet" href="../../css/mySidebar.css">
  <!-- 本页私有css -->
  <link rel="stylesheet" href="../../css/pageCss/suggest.css">
  <!-- 综合-script -->
  <script src="../../script/index.js" defer></script>
  <!-- 产品分类模块 -->
  <script src="../../script/productCategory.js" defer></script>
  <!-- 所有页面组成部分 -->
  <script src="../../components/common.js" defer></script>
  <script src="../../components/mySidebar.js" defer></script>
  <!-- 单页面组成部分 -->
  <!-- <script src="./components/page.js" defer></script> -->
</head>

<body>
  <!-- 引入头部组件 -->
  <div id="header"></div>

  <div class="main">
    <!-- 面包屑 -->
    <div class="breadBox">
      <div onclick="toRouter(this)" data-link="../index/index.html">首页</div>
      <div>></div>
      <div class="textSelect" onclick="toRouter(this)" data-link="./providerDetail.html">应用支持</div>
    </div>
    <div class="dataDown">
      <!-- 侧边栏 -->
      <div class="mySidebar"></div>


      <div class="dataDown-right">
        <div class="search">
          <div class="h3">建议反馈</div>
        </div>
        <p class="tip">为了更及时的处理您的问题并回复，请您选择提交问题涉及的领域</p>
        <form class="layui-form">
          <div class="layui-input-block">
            <div class="layui-form-item">
              <input type="checkbox" name="problemType" value="problemType1" title="网站问题" lay-filter="problemType">
              <input type="checkbox" name="problemType" value="problemType2" title="产品问题" checked lay-verify="required"
                lay-filter="problemType">
              <input type="checkbox" name="problemType" value="problemType3" title="技术问题" lay-filter="problemType">
              <input type="checkbox" name="problemType" value="problemType4" title="其他问题" lay-filter="problemType">
            </div>
          </div>
          <div class="layui-col-md6">
            <div class="layui-form-item">
              <label class="layui-form-label">姓名</label>
              <div class="layui-input-block">
                <input type="text" name="uName" lay-verify="required" placeholder="请输入" autocomplete="off"
                  class="layui-input" value="1">
              </div>
            </div>
          </div>
          <div class="layui-col-md6">
            <div class="layui-form-item">
              <label class="layui-form-label">邮箱</label>
              <div class="layui-input-block">
                <input type="text" name="email" lay-verify="required|email" placeholder="有值时才校验" autocomplete="off"
                  class="layui-input" value="<EMAIL>">
              </div>
            </div>
          </div>
          <div class="layui-col-md6">
            <div class="layui-form-item">
              <label class="layui-form-label">手机</label>
              <div class="layui-input-block">
                <input type="tel" name="phone" lay-verify="required|phone" autocomplete="off" lay-reqtext="请填写手机号"
                  lay-affix="clear" value="13800000000" class="layui-input demo-phone">
              </div>
            </div>
          </div>
          <div class="layui-col-md6"></div>
          <div class="layui-form-item">
            <div class="layui-col-md6">
              <label class="layui-form-label">省份</label>
              <div class="layui-input-block">
                <select name="quiz1" lay-verify="required">
                  <option value="" selected>请选择省</option>
                  <option value="浙江">浙江省</option>
                  <option value="你的工号">江西省</option>
                  <option value="你最喜欢的老师">福建省</option>
                </select>
              </div>
            </div>
            <div class="layui-col-md6">
              <label class="layui-form-label">城市</label>
              <div class="layui-input-block">
                <select name="quiz2" lay-verify="required">
                  <option value="">请选择市</option>
                  <option value="杭州">杭州</option>
                  <option value="宁波" disabled>宁波</option>
                  <option value="温州">温州</option>
                  <option value="温州">台州</option>
                  <option value="温州">绍兴</option>
                </select>
              </div>
            </div>


          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">产品类型</label>
            <div class="layui-input-block pic">
              <div class="layui-col-md6">
                <input type="checkbox" checked name="productType" value="ACDC" title="AC/DC电源模块"
                  lay-filter="productType">
                <img src="../../images/power/3w5.png" alt="">
              </div>
              <div class="layui-col-md5">
                <input type="checkbox" name="productType" value="DCDC" title="DC/DC电源模块" lay-filter="productType">
                <img src="../../images/power/3w5.png" alt="">
              </div>
            </div>
          </div>


          <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">问题或建议</label>
            <div class="layui-input-block">
              <textarea id="myTextarea" placeholder="请输入内容" lay-verify="required" class="layui-textarea"></textarea>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">验证码</label>
            <div class="layui-input-inline">
              <div class="layui-row">
                <div class="layui-col-xs7">
                  <div class="layui-input-wrap">
                    <input type="text" name="captcha" value="" lay-verify="required" placeholder="" lay-reqtext="请填写验证码"
                      autocomplete="off" class="layui-input" lay-affix="clear">
                  </div>
                </div>
                <div class="layui-col-xs5">
                  <div style="margin-left: 10px;">
                    <img src="https://www.oschina.net/action/user/captcha"
                      onclick="this.src='https://www.oschina.net/action/user/captcha?t='+ new Date().getTime();">
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="layui-col-xs4">&nbsp;</div>
          <div class="layui-col-xs4">
            <div class="layui-form-item">
              <button class="layui-btn layui-bg-blue" lay-submit lay-filter="demo1">确认</button>
            </div>
          </div>
        </form>

      </div>


    </div>

  </div>
  </div>

  <div class="bug"></div>

  <!-- 引入底部组件 -->
  <div id="footer"></div>
</body>
<script>
  const data = [
    {
      text: '联系我们',
      link: './contactInfo.html',
    },
    {
      text: '建议反馈',
      link: './suggest.html',
    },
    {
      text: '联系信息',
      link: './contactInfo.html',
    },
  ]
  document.getElementById("myTextarea").value = "新的文本内容";

  document.addEventListener('DOMContentLoaded', function () {
    layui.use(['form', 'laydate', 'util'], function () {
      var form = layui.form;
      var layer = layui.layer;
      var laydate = layui.laydate;
      var util = layui.util;


      // checkbox 改造成单选功能 - 问题类型 (点击自身不能取消)
      form.on('checkbox(problemType)', function (data) {
        // console.log('问题类型:', data);
        if (data.elem.checked) {
          // 取消同组其他checkbox的选中状态
          $('input[name="problemType"]').not(data.elem).prop('checked', false);
          // 重新渲染表单
          form.render('checkbox');
        } else {
          // 点击元素本身防止取消选择
          $(data.elem).prop('checked', true);
          form.render('checkbox');
        }
      });

      // 产品类型 - 多选功能
      form.on('checkbox(productType)', function (data) {
        console.log('产品类型', data);
        // 获取所有选中的产品类型值
        var selectedProductTypes = [];
        $('input[name="productType"]:checked').each(function () {
          selectedProductTypes.push({
            value: $(this).val(),
            title: $(this).attr('title')
          });
        });
        console.log('Selected product types:', selectedProductTypes);
        console.log('Selected product type values:', selectedProductTypes.map(item => item.value));
      });

      // 提交事件
      form.on('submit(demo1)', function (data) {
        var field = data.field; // 获取表单字段值

        // 获取产品类型（多选）
        // var selectedProductTypes = [];
        // $('input[name="productType"]:checked').each(function () {
        //   selectedProductTypes.push({
        //     value: $(this).val(),
        //     title: $(this).attr('title')
        //   });
        // });

        // 显示填写结果
        console.log('field :>> ', field);
        console.log('产品类型:', selectedProductTypes);
        console.log('产品类型值:', selectedProductTypes.map(item => item.value));

        // 此处可执行 Ajax 等操作
        // …

        return false; // 阻止默认 form 跳转
      });


      // 日期
      // laydate.render({
      //   elem: '#date'
      // });





    });
  });








</script>


</html>