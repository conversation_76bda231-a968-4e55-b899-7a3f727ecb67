<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Checkbox Single Select Test</title>
  <link rel="stylesheet" href="../modules/layui/css/layui.css">
  <script src="../modules/jq.js"></script>
  <script src="../modules/layui/layui.js"></script>
</head>
<body>
  <div style="padding: 20px;">
    <h2>Checkbox Single Select Test</h2>
    
    <form class="layui-form">
      <div class="layui-form-item">
        <label class="layui-form-label">问题类型</label>
        <div class="layui-input-block">
          <input type="checkbox" name="problemType" value="1" title="网站问题" lay-filter="problemType">
          <input type="checkbox" name="problemType" value="2" title="产品问题" checked lay-filter="problemType">
          <input type="checkbox" name="problemType" value="3" title="技术问题" lay-filter="problemType">
          <input type="checkbox" name="problemType" value="4" title="其他问题" lay-filter="problemType">
        </div>
      </div>
      
      <div class="layui-form-item">
        <label class="layui-form-label">产品类型</label>
        <div class="layui-input-block">
          <input type="checkbox" name="productType" value="1" title="AC/DC电源模块" lay-filter="productType">
          <input type="checkbox" name="productType" value="2" title="DC/DC电源模块" lay-filter="productType">
        </div>
      </div>
      
      <div class="layui-form-item">
        <button type="button" class="layui-btn" onclick="showValues()">显示选中值</button>
      </div>
    </form>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      layui.use(['form'], function () {
        var form = layui.form;
        
        console.log('Form initialized');
        
        // checkbox 改造成单选功能 - 问题类型
        form.on('checkbox(problemType)', function (data) {
          console.log('Problem type checkbox clicked:', data);
          if (data.elem.checked) {
            // 取消同组其他checkbox的选中状态
            $('input[name="problemType"]').not(data.elem).prop('checked', false);
            // 重新渲染表单
            form.render('checkbox');
            console.log('Problem type updated, selected value:', data.value);
          }
        });
        
        // checkbox 改造成单选功能 - 产品类型
        form.on('checkbox(productType)', function (data) {
          console.log('Product type checkbox clicked:', data);
          if (data.elem.checked) {
            // 取消同组其他checkbox的选中状态
            $('input[name="productType"]').not(data.elem).prop('checked', false);
            // 重新渲染表单
            form.render('checkbox');
            console.log('Product type updated, selected value:', data.value);
          }
        });
      });
    });
    
    function showValues() {
      var problemType = $('input[name="problemType"]:checked').val();
      var productType = $('input[name="productType"]:checked').val();
      
      console.log('Selected problem type:', problemType);
      console.log('Selected product type:', productType);
      
      alert('问题类型: ' + (problemType || '未选择') + '\n产品类型: ' + (productType || '未选择'));
    }
  </script>
</body>
</html>
