<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Checkbox Single Select Test</title>
  <link rel="stylesheet" href="../modules/layui/css/layui.css">
  <script src="../modules/jq.js"></script>
  <script src="../modules/layui/layui.js"></script>
</head>

<body>
  <div style="padding: 20px;">
    <h2>Checkbox Single Select Test</h2>
    <p style="color: #666; margin-bottom: 20px;">
      <strong>问题类型:</strong> 单选功能，点击自身不能取消选择<br>
      <strong>产品类型:</strong> 多选功能，可以选择多个选项
    </p>

    <form class="layui-form">
      <div class="layui-form-item">
        <label class="layui-form-label">问题类型</label>
        <div class="layui-input-block">
          <input type="checkbox" name="problemType" value="1" title="网站问题" lay-filter="problemType">
          <input type="checkbox" name="problemType" value="2" title="产品问题" checked lay-filter="problemType">
          <input type="checkbox" name="problemType" value="3" title="技术问题" lay-filter="problemType">
          <input type="checkbox" name="problemType" value="4" title="其他问题" lay-filter="problemType">
        </div>
      </div>

      <div class="layui-form-item">
        <label class="layui-form-label">产品类型</label>
        <div class="layui-input-block">
          <input type="checkbox" name="productType" value="1" title="AC/DC电源模块" lay-filter="productType">
          <input type="checkbox" name="productType" value="2" title="DC/DC电源模块" lay-filter="productType">
        </div>
      </div>

      <div class="layui-form-item">
        <button type="button" class="layui-btn" onclick="showValues()">显示选中值</button>
      </div>
    </form>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      layui.use(['form'], function () {
        var form = layui.form;

        console.log('Form initialized');

        // 获取产品类型值的辅助函数
        window.getProductTypeValues = function () {
          var selectedProductTypes = [];
          $('input[name="productType"]:checked').each(function () {
            selectedProductTypes.push({
              value: $(this).val(),
              title: $(this).attr('title')
            });
          });
          return selectedProductTypes;
        };

        // 获取问题类型值的辅助函数
        window.getProblemTypeValue = function () {
          return $('input[name="problemType"]:checked').val();
        };

        // checkbox 改造成单选功能 - 问题类型 (点击自身不能取消)
        form.on('checkbox(problemType)', function (data) {
          console.log('Problem type checkbox clicked:', data);
          if (data.elem.checked) {
            // 取消同组其他checkbox的选中状态
            $('input[name="problemType"]').not(data.elem).prop('checked', false);
            // 重新渲染表单
            form.render('checkbox');
            console.log('Problem type updated, selected value:', data.value);
          } else {
            // 如果试图取消选中，重新选中它（防止取消选择）
            $(data.elem).prop('checked', true);
            form.render('checkbox');
            console.log('Prevented deselection of problem type:', data.value);
          }
        });

        // 产品类型 - 多选功能
        form.on('checkbox(productType)', function (data) {
          console.log('Product type checkbox clicked:', data);
          // 获取所有选中的产品类型值
          var selectedProductTypes = [];
          $('input[name="productType"]:checked').each(function () {
            selectedProductTypes.push({
              value: $(this).val(),
              title: $(this).attr('title')
            });
          });
          console.log('Selected product types:', selectedProductTypes);
          console.log('Selected product type values:', selectedProductTypes.map(item => item.value));
        });
      });
    });

    function showValues() {
      // 使用辅助函数获取值
      var problemType = getProblemTypeValue();
      var selectedProductTypes = getProductTypeValues();

      console.log('Selected problem type:', problemType);
      console.log('Selected product types:', selectedProductTypes);

      var productTypeText = selectedProductTypes.length > 0
        ? selectedProductTypes.map(item => item.title).join(', ')
        : '未选择';

      alert('问题类型: ' + (problemType ? $('input[name="problemType"][value="' + problemType + '"]').attr('title') : '未选择') +
        '\n产品类型: ' + productTypeText +
        '\n产品类型值: ' + selectedProductTypes.map(item => item.value).join(', '));
    }
  </script>
</body>

</html>