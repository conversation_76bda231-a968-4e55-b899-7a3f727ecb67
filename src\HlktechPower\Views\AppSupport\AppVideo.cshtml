﻿@using HlktechPower.Entity
<head>
    <!-- 本页私有css -->
    <link rel="stylesheet" href="~/css/pageCss/videos.css">
    <link rel="stylesheet" href="~/css/mySidebar.css">
    <!-- 所有页面组成部分 -->
    <script src="~/components/mySidebar.js" defer></script>
</head>
<body>


    <div class="main">
        <!-- 面包屑 -->
        <div class="breadBox">
            <div onclick="toRouter(this)" data-link="/">@T("首页")</div>
            <div>></div>
            <div class="textSelect" onclick="toRouter(this)" data-link="#">@T("应用支持")</div>
        </div>
        <div class="dataDown">
            <!-- 侧边栏 -->
            <div class="mySidebar"></div>
            <div class="dataDown-right">
                <div class="search">
                    <div class="h3">@T("应用视频")</div>
                    <div class="layui-input-group">
                        <input type="text" placeholder="" class="layui-input" value="@Model.key" id="searchKey">
                        <div class="layui-input-split layui-input-suffix" id="sousuo">
                            <i class="iconfont icon-search"></i>&nbsp;<span class="wen">@T("搜索")</span>
                        </div>
                    </div>
                </div>
                <div class="videoBox">
                    <!--   下载功能
                    nodownload 即可隐藏
                    播放速度功能
                    noplaybackrate 即可隐藏
                    画中画功能
                    disablePictureInPicture=“true” -->

                    @foreach (AppVideo item in Model.list)
                    {
                        <div class="videoItem">
                            <video src="@item.Url" width="100%" height="180" controls
                                   controlsList="nodownload noplaybackrate " disablePictureInPicture=“false” Id="@item.Id">
                            </video>
                            <div class="play-button"></div>
                            <div class="pause-button"></div>
                            <div class="title">【@T("产品推荐")】@item.Name</div>
                            <div class="num">@T("{0}人观看", item.ViewCount)</div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <div class="bug"></div>

</body>
<script>
    const data = [
        {  text: '@T("应用支持")', link: '#' },  // 标题
        {  text: '@T("焦点专题")', link: '@Url.Action("Index")' },
        {  text: '@T("资料下载")', link: '@Url.Action("DataDownload")' },
        {  text: '@T("应用视频")', link: '@Url.Action("AppVideo")' },
        {  text: '@T("常见问题")', link: '@Url.Action("CommonProblem")' },
        {  text: '@T("样品申请")', link: '@Url.Action("SampleApply")' },
        {  text: '@T("成品检测报告")', link: '@Url.Action("ProductInspectionReport")' }
    ];

    // 视频播放控制
    document.addEventListener('DOMContentLoaded', function () {
        const videoItems = document.querySelectorAll('.videoItem');

        // 暂停所有其他视频的函数
        function pauseAllOtherVideos(currentVideo) {
            videoItems.forEach(item => {
                const video = item.querySelector('video');
                const playButton = item.querySelector('.play-button');
                const pauseButton = item.querySelector('.pause-button');

                if (video !== currentVideo && !video.paused) {
                    video.pause();
                    playButton.style.display = 'flex';
                    pauseButton.style.display = 'none';
                    pauseButton.style.opacity = '0';
                    item.classList.remove('playing'); // 移除播放状态类
                }
            });
        }

        videoItems.forEach(item => {
            const video = item.querySelector('video');
            const playButton = item.querySelector('.play-button');
            const pauseButton = item.querySelector('.pause-button');
            let hoverTimeout;

            // 点击播放按钮播放视频
            playButton.addEventListener('click', function () {
                UpdateViewCount(video.id);//修改播放量
                pauseAllOtherVideos(video); // 暂停其他视频
                video.play();
                playButton.style.display = 'none';
                item.classList.add('playing'); // 添加播放状态类
            });

            // 点击暂停按钮暂停视频
            pauseButton.addEventListener('click', function () {
                video.pause();
                playButton.style.display = 'flex';
                pauseButton.style.display = 'none';
                pauseButton.style.opacity = '0';
                item.classList.remove('playing'); // 移除播放状态类
            });

            // 点击视频暂停/播放
            video.addEventListener('click', function (e) {
                console.log('video click event - paused:', video.paused, video);

                // 阻止默认行为和事件冒泡
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();

                // 添加延迟以避免事件冲突
                setTimeout(() => {
                    if (video.paused) {
                        UpdateViewCount(video.id);//修改播放量
                        pauseAllOtherVideos(video); // 暂停其他视频
                        video.play();
                        playButton.style.display = 'none';
                        item.classList.add('playing'); // 添加播放状态类
                    } else {
                        console.log('Attempting to pause video...');
                        video.pause();
                        // 暂停后的UI更新现在由pause事件处理器处理
                    }
                }, 10);
            });





            // 监听视频开始播放事件
            video.addEventListener('play', function () {
                pauseAllOtherVideos(video); // 暂停其他视频
                item.classList.add('playing'); // 添加播放状态类
            });

            // 监听视频暂停事件
            video.addEventListener('pause', function () {
                playButton.style.display = 'flex';
                pauseButton.style.display = 'none';
                pauseButton.style.opacity = '0';
                item.classList.remove('playing'); // 移除播放状态类
            });

            // 鼠标悬停在视频上时显示暂停按钮（仅在播放时）
            item.addEventListener('mouseenter', function () {
                if (!video.paused) {
                    clearTimeout(hoverTimeout);
                    pauseButton.style.display = 'flex';
                    setTimeout(() => {
                        pauseButton.style.opacity = '1';
                    }, 10);
                }
            });

            // 鼠标离开时隐藏暂停按钮
            item.addEventListener('mouseleave', function () {
                pauseButton.style.opacity = '0';
                hoverTimeout = setTimeout(() => {
                    pauseButton.style.display = 'none';
                }, 300);
            });

            // 视频结束时显示播放按钮
            video.addEventListener('ended', function () {
                playButton.style.display = 'flex';
                pauseButton.style.display = 'none';
                pauseButton.style.opacity = '0';
                item.classList.remove('playing'); // 移除播放状态类
            });

        });
    });
</script>
<script asp-location="Footer">
    $("#sousuo").click(function(){
        var key = $("#searchKey").val();
        window.location.href = '@Url.Action("AppVideo")?key='+key+'';
    });

    //修改下载量并下载文件
    function UpdateViewCount(Id){
        $.post('@Url.Action("UpdateViewCount")',{Id},function(res){
            if(res.success){

            }else{
                layui.layer.msg(res.msg);
            }
        })
    }
</script>