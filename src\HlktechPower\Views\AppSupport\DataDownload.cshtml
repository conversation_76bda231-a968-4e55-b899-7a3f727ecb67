﻿@using HlktechPower.Entity
<head>
    <link rel="stylesheet" href="~/css/mySidebar.css">
    <!-- 本页私有css -->
    <link rel="stylesheet" href="~/css/pageCss/downloads.css">
    <!-- 综合-script -->
    <script src="~/components/mySidebar.js" defer></script>

</head>

<body>
    <!-- 引入头部组件 -->
    <div id="header"></div>

    <div class="main">
        <!-- 面包屑 -->
        <div class="breadBox">
            <div onclick="toRouter(this)" data-link="/">@T("首页")</div>
            <div>></div>
            <div class="textSelect" onclick="toRouter(this)" data-link="@Url.Action("Index")">@T("应用支持")</div>
        </div>
        <div class="dataDown">
            <!-- 侧边栏 -->
            <div class="mySidebar"></div>
            <div class="dataDown-right">
                <div class="search">
                    <div class="h3">@T("资料下载")</div>
                    <div class="layui-input-group">
                        <input type="text" placeholder="" class="layui-input" id="searchKey" value="@Model.key">
                        <div class="layui-input-split layui-input-suffix" id="sousuo">
                            <i class="iconfont icon-search"></i>&nbsp;<span class="wen">@T("搜索")</span>
                        </div>
                    </div>
                </div>
                <div class="layui-tab layui-tab-brief">
                    <ul class="layui-tab-title">
                        <li class="layui-this">@T("产品目录")</li>
                        <li>@T("应用笔记")</li>
                        <li>@T("配置文件")</li>
                        <li>@T("常见故障分析")</li>
                    </ul>
                    <div class="layui-tab-content">
                        <div class="layui-tab-item layui-show">
                            <ul class="item">
                                <li>
                                    <div>@T("标题")</div>
                                    <div>@T("下载")</div>
                                </li>
                                @foreach (DataFile item in Model.list0)
                                {
                                    <li>
                                        <div>@item.Name</div>
                                        <div onclick="UpdateDownloadVolume('@DHSetting.Current.CurDomainUrl/@item.Url','@item.Id')"><img src="~/images/power/xiazai.png" alt=""></div>
                                    </li>
                                }
                            </ul>
                        </div>
                        <div class="layui-tab-item">
                            <ul class="item">
                                <li>
                                    <div>@T("标题")</div>
                                    <div>@T("下载")</div>
                                </li>
                                @foreach (DataFile item in Model.list1)
                                {
                                    <li>
                                        <div>@item.Name</div>
                                        <div onclick="UpdateDownloadVolume('@DHSetting.Current.CurDomainUrl/@item.Url','@item.Id')"><img src="~/images/power/xiazai.png" alt=""></div>
                                    </li>
                                }
                            </ul>
                        </div>
                        <div class="layui-tab-item">
                            <ul class="item">
                                <li>
                                    <div>@T("标题")</div>
                                    <div>@T("下载")</div>
                                </li>
                                @foreach (DataFile item in Model.list2)
                                {
                                    <li>
                                        <div>@item.Name</div>
                                        <div onclick="UpdateDownloadVolume('@DHSetting.Current.CurDomainUrl/@item.Url','@item.Id')"><img src="~/images/power/xiazai.png" alt=""></div>
                                    </li>
                                }
                            </ul>
                        </div>
                        <div class="layui-tab-item">
                            <ul class="item">
                                <li>
                                    <div>@T("标题")</div>
                                    <div>@T("下载")</div>
                                </li>
                                @foreach (DataFile item in Model.list3)
                                {
                                    <li>
                                        <div>@item.Name</div>
                                        <div onclick="UpdateDownloadVolume('@DHSetting.Current.CurDomainUrl/@item.Url','@item.Id')"><img src="~/images/power/xiazai.png" alt=""></div>
                                    </li>
                                }
                            </ul>
                        </div>

                    </div>
                </div>


            </div>

        </div>
    </div>

    <div class="bug"></div>

</body>
<script>
    const data = [
        {  text: '@T("应用支持")', link: '#' },  // 标题
        {  text: '@T("焦点专题")', link: '@Url.Action("Index")' },
        {  text: '@T("资料下载")', link: '@Url.Action("DataDownload")' },
        {  text: '@T("应用视频")', link: '@Url.Action("AppVideo")' },
        {  text: '@T("常见问题")', link: '@Url.Action("CommonProblem")' },
        {  text: '@T("样品申请")', link: '@Url.Action("SampleApply")' },
        {  text: '@T("成品检测报告")', link: '@Url.Action("ProductInspectionReport")' }
    ];
</script>
<script asp-location="Footer">
    $("#sousuo").click(function(){
         // 获取当前选中tab的jQuery对象
         var $selectedTab = $('.layui-tab-title li.layui-this');
        // 获取索引
        var selectedIndex = $selectedTab.index();
        //console.log(selectedIndex)
        var key = $("#searchKey").val();
        window.location.href = '@Url.Action("DataDownload")?key='+key+'&dataFileClass='+selectedIndex+'';
    });

    //修改下载量并下载文件
    function UpdateDownloadVolume(url,Id){
        $.post('@Url.Action("UpdateDownloadVolume")',{Id},function(res){
            //console.log(111,res);
            if(res.success){
                download(url)
            }else{
                layui.layer.msg(res.msg);
            }
        })
    }
</script>