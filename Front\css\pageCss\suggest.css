.h3 {
  margin-bottom: 10px;
}

.tip {
  padding-bottom: 20px;
  border-bottom: 2px solid var(--myBlue);
  margin-bottom: 20px;
}
.tip a{
  text-decoration:underline;
  color: var(--myBlue);
}

.layui-form {
  width: 80%;
  margin: 0 auto;
 
}
.layui-input{
  border-color: var(--line3)  ;
}

.layui-form-item {
  margin-bottom: 10px;
}

.layui-form-select dl {
  height: 150px;
}

.iconBox {
  display: flex;
  align-items: baseline;
  gap: 10px;

}


.icon {
  display: flex;
  align-content: center;
  width: 20px;
  height: 20px;
  margin-top: 8px;
  margin-left: 20px;
  background-color: var(--myBlue);
  color:var(--white) ;
  border-radius: 50%;
}
.layui-icon-subtraction,
.layui-icon-addition {
  position: relative;
  left: 2px;
  top: 1px;
}

.layui-row {
  width: 20vw;
}

.layui-btn {
  margin-top: 30px;
}
.remove-row {
  cursor: pointer;
}
.layui-input-affix .layui-icon{
  color: var(--white);
}
.pic img{
  width: 36px;
}
/* 将radio样式改为类似checkbox */

.pic .layui-col-md5{
  margin-left: 1vw;
}