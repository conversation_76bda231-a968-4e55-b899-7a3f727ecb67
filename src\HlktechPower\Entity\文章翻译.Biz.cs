﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;
using System.Xml.Serialization;
using NewLife;
using NewLife.Data;
using NewLife.Log;
using NewLife.Model;
using NewLife.Reflection;
using NewLife.Threading;
using NewLife.Web;
using XCode;
using XCode.Cache;
using XCode.Configuration;
using XCode.DataAccessLayer;
using XCode.Membership;
using XCode.Shards;

namespace HlktechPower.Entity;

public partial class ArticleLan : DHEntityBase<ArticleLan>
{
    #region 对象操作
    static ArticleLan()
    {
        // 累加字段，生成 Update xx Set Count=Count+1234 Where xxx
        //var df = Meta.Factory.AdditionalFields;
        //df.Add(nameof(AId));

        // 过滤器 UserModule、TimeModule、IPModule

        // 实体缓存
        // var ec = Meta.Cache;
        // ec.Expire = 60;
    }

    /// <summary>验证并修补数据，返回验证结果，或者通过抛出异常的方式提示验证失败。</summary>
    /// <param name="method">添删改方法</param>
    public override Boolean Valid(DataMethod method)
    {
        //if (method == DataMethod.Delete) return true;
        // 如果没有脏数据，则不需要进行任何处理
        if (!HasDirty) return true;

        // 建议先调用基类方法，基类方法会做一些统一处理
        if (!base.Valid(method)) return false;

        // 在新插入数据或者修改了指定字段时进行修正

        // 检查唯一索引
        // CheckExist(method == DataMethod.Insert, nameof(AId), nameof(LId));

        return true;
    }

    ///// <summary>首次连接数据库时初始化数据，仅用于实体类重载，用户不应该调用该方法</summary>
    //[EditorBrowsable(EditorBrowsableState.Never)]
    //protected override void InitData()
    //{
    //    // InitData一般用于当数据表没有数据时添加一些默认数据，该实体类的任何第一次数据库操作都会触发该方法，默认异步调用
    //    if (Meta.Session.Count > 0) return;

    //    if (XTrace.Debug) XTrace.WriteLine("开始初始化ArticleLan[文章翻译]数据……");

    //    var entity = new ArticleLan();
    //    entity.AId = 0;
    //    entity.LId = 0;
    //    entity.Name = "abc";
    //    entity.Content = "abc";
    //    entity.Summary = "abc";
    //    entity.Pic = "abc";
    //    entity.SeoTitle = "abc";
    //    entity.SeoKey = "abc";
    //    entity.SeoDescription = "abc";
    //    entity.Insert();

    //    if (XTrace.Debug) XTrace.WriteLine("完成初始化ArticleLan[文章翻译]数据！");
    //}

    ///// <summary>已重载。基类先调用Valid(true)验证数据，然后在事务保护内调用OnInsert</summary>
    ///// <returns></returns>
    //public override Int32 Insert()
    //{
    //    return base.Insert();
    //}

    ///// <summary>已重载。在事务保护范围内处理业务，位于Valid之后</summary>
    ///// <returns></returns>
    //protected override Int32 OnDelete()
    //{
    //    return base.OnDelete();
    //}
    #endregion

    #region 扩展属性
    #endregion

    #region 高级查询
    /// <summary>高级查询</summary>
    /// <param name="aId">文章Id</param>
    /// <param name="lId">所属语言Id</param>
    /// <param name="name">文章标题</param>
    /// <param name="key">关键字</param>
    /// <param name="page">分页参数信息。可携带统计和数据权限扩展查询等信息</param>
    /// <returns>实体列表</returns>
    public static IList<ArticleLan> Search(Int32 aId, Int32 lId, String? name, String key, PageParameter page)
    {
        var exp = new WhereExpression();

        if (aId >= 0) exp &= _.AId == aId;
        if (lId >= 0) exp &= _.LId == lId;
        if (!name.IsNullOrEmpty()) exp &= _.Name == name;
        if (!key.IsNullOrEmpty()) exp &= SearchWhereByKeys(key);

        return FindAll(exp, page);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="model"></param>
    /// <param name="LId"></param>
    /// <returns></returns>
    public static ArticleLan FindByArticle(Article model,Int32 LId)
    {
        var item = FindByAIdAndLId(model.Id, LId) ?? new ArticleLan();
        if (item.Name.IsNullOrWhiteSpace()) item.Name = model.Name;
        if (item.Summary.IsNullOrWhiteSpace()) item.Summary = model.Summary;
        if (item.Content.IsNullOrWhiteSpace()) item.Content = model.Content;
        if (item.Pic.IsNullOrWhiteSpace()) item.Pic = model.Pic;
        return item;
    }

    // Select Count(Id) as Id,Category From DH_ArticleLan Where CreateTime>'2020-01-24 00:00:00' Group By Category Order By Id Desc limit 20
    //static readonly FieldCache<ArticleLan> _CategoryCache = new(nameof(Category))
    //{
    //Where = _.CreateTime > DateTime.Today.AddDays(-30) & Expression.Empty
    //};

    ///// <summary>获取类别列表，字段缓存10分钟，分组统计数据最多的前20种，用于魔方前台下拉选择</summary>
    ///// <returns></returns>
    //public static IDictionary<String, String> GetCategoryList() => _CategoryCache.FindAllName();
    #endregion

    #region 业务操作
    public IArticleLan ToModel()
    {
        var model = new ArticleLan();
        model.Copy(this);

        return model;
    }

    #endregion
}
