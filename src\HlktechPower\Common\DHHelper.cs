﻿using DH.Entity;
using HlktechPower.Entity;

using NewLife.Log;

using XCode;

namespace HlktechPower.Common;

public class DHHelper {
    /// <summary>
    /// 初始化本地化字符串资源
    /// </summary>
    public static void InitLocaleStringResource()
    {
        LocaleStringResource.InitInsert("首页", "首页", "首頁", "Home Page");
        LocaleStringResource.InitInsert("产品分类", "产品分类", "産品分類", "Product Class");
        LocaleStringResource.InitInsert("产品中心", "产品中心", "産品中心", "Product Center");
        LocaleStringResource.InitInsert("企业动态", "企业动态", "企業動態", "Enterprise Dynamics");
        LocaleStringResource.InitInsert("产品动态", "产品动态", "産品動態", "Product Dynamics");
        LocaleStringResource.InitInsert("型号标题", "型号标题", "型號標題", "Model Title");
        LocaleStringResource.InitInsert("产品选型", "产品选型", "産品選型", "Product Selection");
        LocaleStringResource.InitInsert("样品申请", "样品申请", "样品申請", "Sample Apply");
        LocaleStringResource.InitInsert("联系我们", "联系我们", "聯系我們", "Contact Us");
        LocaleStringResource.InitInsert("应用支持", "应用支持", "應用支持", "App Support");
        LocaleStringResource.InitInsert("焦点专题", "焦点专题", "焦點專題", "Focus Topic");
        LocaleStringResource.InitInsert("资料下载", "资料下载", "資料下載", "Data Download");
        LocaleStringResource.InitInsert("常见问题", "常见问题", "常見問題", "Common Problem");
        LocaleStringResource.InitInsert("技术应用", "技术应用", "技术應用", "Technology App");
        LocaleStringResource.InitInsert("查看更多", "查看更多", "查看更多", "View More");
        LocaleStringResource.InitInsert("搜索", "搜索", "搜索", "Search");
        LocaleStringResource.InitInsert("应用视频", "应用视频", "應用視頻", "App Video");
        LocaleStringResource.InitInsert("产品目录", "产品目录", "産品目錄", "Product Catalogue");
        LocaleStringResource.InitInsert("应用笔记", "应用笔记", "應用筆記", "App Notes");
        LocaleStringResource.InitInsert("配置文件", "配置文件", "配置文件", "Config File");
        LocaleStringResource.InitInsert("标题", "标题", "標題", "Title");
        LocaleStringResource.InitInsert("下载", "下载", "下載", "Download");
        LocaleStringResource.InitInsert("新闻动态", "新闻动态", "新聞動態", "News Dynamics");
        LocaleStringResource.InitInsert("关于我们", "关于我们", "關于我們", "About Us");
        LocaleStringResource.InitInsert("智能选型", "智能选型", "智能選型", "Intelligence Selection");
        LocaleStringResource.InitInsert("企业简介", "企业简介", "企業簡介", "Company Profile");
        LocaleStringResource.InitInsert("企业文化", "企业文化", "企業文化", "Corporate Culture");
        LocaleStringResource.InitInsert("企业历程", "企业历程", "企業歷程", "Enterprise History");
        LocaleStringResource.InitInsert("加入我们", "加入我们", "加入我們", "Join Us");
        LocaleStringResource.InitInsert("联系信息", "联系信息", "聯系信息", "Contact Information");
        LocaleStringResource.InitInsert("功能板块", "功能板块", "功能板塊", "Functional Section");
        LocaleStringResource.InitInsert("快速定位满足您需求的产品", "快速定位满足您需求的产品", "快速定位滿足您需求的産品", "Quickly locate the products that meet your needs");
        LocaleStringResource.InitInsert("快速响应您的业务需求", "快速响应您的业务需求", "快速響應您的業務需求", "Respond quickly to your business needs");
        LocaleStringResource.InitInsert("性能优越，配套解决方案", "性能优越，配套解决方案", "性能優越，配套解決方案", "Superior performance and supporting solutions");
        LocaleStringResource.InitInsert("6000+产品免费快速申请", "6000+产品免费快速申请", "6000+産品免費快速申請", "Over 6,000 products can be applied for free and quickly");
        LocaleStringResource.InitInsert("产品型号", "产品型号", "産品型號", "Product Model");
        LocaleStringResource.InitInsert("数量", "数量", "數量", "Quantity");
        LocaleStringResource.InitInsert("请选择", "请选择", "請選擇", "Please Select");
        LocaleStringResource.InitInsert("请输入", "请输入", "請輸入", "Please Enter");
        LocaleStringResource.InitInsert("项目名称", "项目名称", "項目名稱", "Project Name");
        LocaleStringResource.InitInsert("项目联系人", "项目联系人", "項目聯系人", "Project Contact Person");
        LocaleStringResource.InitInsert("公司名称", "公司名称", "公司名稱", "Company Name");
        LocaleStringResource.InitInsert("联系电话", "联系电话", "聯系電話", "Phone");
        LocaleStringResource.InitInsert("邮箱", "邮箱", "郵箱", "Mail");
        LocaleStringResource.InitInsert("所在区域", "所在区域", "所在區域", "Region");
        LocaleStringResource.InitInsert("详细地址", "详细地址", "詳細地址", "Detailed Address");
        LocaleStringResource.InitInsert("申请主题", "申请主题", "申請主題", "Application Theme");
        LocaleStringResource.InitInsert("需求描述", "需求描述", "需求描述", "Demand Description");
        LocaleStringResource.InitInsert("请选择省", "请选择省", "請選擇省", "Please select the province");
        LocaleStringResource.InitInsert("请选择市", "请选择市", "請選擇市", "Please select the city");
        LocaleStringResource.InitInsert("确认", "确认", "確認", "Confirm");
        LocaleStringResource.InitInsert("成品检测报告", "成品检测报告", "成品檢測報告", "Finished Product Inspection Report");
        LocaleStringResource.InitInsert("上一页", "上一页", "上一頁", "Previous Page");
        LocaleStringResource.InitInsert("下一页", "下一页", "下一頁", "Next Page");
        LocaleStringResource.InitInsert("查看详情", "查看详情", "查看詳情", "View Details");
    }

    /// <summary>
    /// 初始化消息模板
    /// </summary>
    public static void InitMsgTpl()
    {
        if (OtherMsgTpl.Meta.Session.Count == 0)
        {
            if (XTrace.Debug) XTrace.WriteLine("开始初始化OtherMsgTpl[其他消息模板]数据……");

            var list1 = new List<OtherMsgTpl>();

            var entity = new OtherMsgTpl();
            entity.MName = "<strong>[用户]</strong>重置密码通知";
            entity.MTitle = "找回密码通知 - {site_name}";
            entity.MCode = "FindPassword";
            entity.MContent = "【海凌科】您正在申请找回登录密码，动态码：{code}，该验证码5分钟内有效，请勿泄露于他人";
            list1.Add(entity);

            entity = new OtherMsgTpl();
            entity.MName = "<strong>[用户]</strong>注册账户通知";
            entity.MTitle = "注册账户通知 - {site_name}";
            entity.MCode = "RegisteredCode";
            entity.MContent = "【海凌科】您正在申请注册会员，动态码：{code}，该验证码5分钟内有效，请勿泄露于他人";
            list1.Add(entity);

            list1.Insert();

            if (XTrace.Debug) XTrace.WriteLine("完成初始化OtherMsgTpl[其他消息模板]数据！");
        }
    }
}