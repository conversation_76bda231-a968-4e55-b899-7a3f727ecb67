.culture {
    display: flex;
    align-items: center;
    margin-bottom: 1.2vw;
    padding-bottom: 1.2vw;
    border-bottom: 1px solid var(--line);
}

.culture-img {
    flex: 0 0 30%;
    margin-right: 4vw;
    margin-top: 1vw;
}

.culture-img img {
    width: 100%;
    height: auto;
    display: block;
}

.culture-list {
    flex: 1;
    list-style: none;
    padding: 0;
    margin: 0;
}

.culture-list li {
    font-size: 1.2rem;
    font-weight: 500;
    line-height: 2;
    color: var(--text-color);
    margin-bottom: .5vw;
    display: flex;
    align-items: center;
}

.culture-list li .block{
    width: .6rem;
    height: .6rem;
    background-color: var(--myBlue);
    transform: rotate(45deg);

}
.culture-list li .block:last-child{
    margin-left: -2px;
    background-color: var(--myBlue);
    opacity: 0.5;
    margin-right: .5vw;
}


.dataDown-right .icon-search,
.wen {
  color: var(--white);
}

.layui-tab-brief>.layui-tab-title .layui-this {
  color: var(--white); 
  background-color: var(--myBlue);

}
.layui-tab .layui-tab-title{
  transform: translateX(22%);
}

.layui-tab .layui-tab-title li {
  margin: 0 1vw;
  border: 1px solid var(--line);
}

.layui-tab-content {
  margin-top: 1vw;
}



.layui-tab .layui-tab-title:after {
  bottom: -20px;
  border: none;
}



/* 取消下划线 */
.layui-tab-title .layui-this:after {
  width: 0;
}
.layui-tab-item img{
  float: left;
  width: 30%;
  margin: 0 1vw;

}


