/* 底部样式 -公共 */
.footer {
    background-color: #f1f1f1;
    position: relative;
    /* border: 2px solid red; */
}

.footer1 {
    background-color: #2C79E8;
    padding: 2vw 0px;
    display: flex;
}

.footer1Content {
    width: 15%;
    color: var(--white);
    display: flex;
    flex-direction: column;
    cursor: pointer;

}
.footer1Content:first-child {
    padding-right: 5%;

}
.footer1Content:last-child {
    display: flex;
    align-items: flex-end;

}

.footer1Content>div:hover {
    color: var(--text-color);

}

.footer2 {
    width: 100%;
    display: flex;
    justify-content: center;
    padding-bottom: 2vw;
    font-size: .8vw;
}

.footer2>div {
    color: var(--text-color2);
    
}

.footer2>div>div {
    margin-top: 1vw;
}

.footer2>div>div:nth-child(1) {
    color: var(--text-color);
    font-size: 1.1vw;
    font-weight: 400;
}

.footer3 {
    border-top: 1px solid var(--text-color1);
    width: 80%;
    margin-left: auto;
    margin-right: auto;
    padding: 2vw 0px;
    color: var(--text-color2);
    text-align: center;
}

.footerLogo>img {
    max-width: 1.2vw;
}

.connectOur {
    color: var(--text-color2);
}