﻿@using HlktechPower.Entity
@{
    var selectProvinceText = T("请选择省");
    var selectCityText = T("请选择市");
}
<head>
    <!-- 基础css -->
    <link rel="stylesheet" href="~/css/mySidebar.css">
    <!-- 本页私有css -->
    <link rel="stylesheet" href="~/css/pageCss/sample.css">
    <!-- 所有页面组成部分 -->
    <script src="~/components/mySidebar.js" defer></script>
</head>

<body>
    <div class="main">
        <!-- 面包屑 -->
        <div class="breadBox">
            <div onclick="toRouter(this)" data-link="/">@T("首页")</div>
            <div>></div>
            <div class="textSelect" onclick="toRouter(this)" data-link="#">@T("应用支持")</div>
        </div>
        <div class="dataDown">
            <!-- 侧边栏 -->
            <div class="mySidebar"></div>


            <div class="dataDown-right">
                <div class="search">
                    <div class="h3">@T("样品申请")</div>
                </div>
                <p class="tip">
                    @T("温馨提示:因资源有限，样品借测或送样，仅针对较大的公司或项目，无法保证全部审批通过，敬请理解。您也可以通过线上平台购买样品测试,(成品7天/模块15天)内测试不合适可退货、退款。")
                </p>
                <form class="layui-form">
                    <div class="layui-form-item">
                        <div class="layui-inline" id="formElements">
                            <label class="layui-form-label">@T("产品型号")</label>
                            <div class="layui-input-inline">
                                <select name="productModel[]" lay-verify="required" lay-search>
                                    <option value="">@T("请选择")</option>
                                    @foreach (ProductClass item in Model.classlist)
                                    {
                                        <option value="@item.Id">@item.Name</option>
                                    }
                                </select>
                            </div>
                            <label class="layui-form-label">@T("数量")</label>
                            <div class="layui-input-inline">
                                <input type="number" name="price_min[]" lay-verify="required" placeholder="" autocomplete="off"
                                class="layui-input" min="1" step="1" lay-affix="number">
                            </div>
                            <div id="addModel" class="layui-input-inline iconBox">
                                <div class="icon">
                                    <!-- <i class="layui-icon layui-icon-subtraction"></i> -->
                                    <i class="layui-icon layui-icon-addition"></i>
                                </div>
                                @* <span @click ="addModel">(点击增加型号)</span> *@
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">@T("项目名称")</label>
                        <div class="layui-input-block">
                            <input type="text" name="projectName" lay-verify="required" placeholder="@T("请输入")" autocomplete="off"
                            class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">@T("预计")@T("月")/@T("年")@T("用量") (@T("个"))</label>
                        <div class="layui-input-block">
                            <input type="text" name="dosageNum" lay-verify="required" placeholder="@T("请输入")" autocomplete="off"
                            class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">@T("项目联系人")</label>
                        <div class="layui-input-block">
                            <input type="text" name="username" lay-verify="required" placeholder="@T("请输入")" autocomplete="off"
                            class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">@T("公司名称")</label>
                        <div class="layui-input-block">
                            <input type="text" name="company" lay-verify="required" placeholder="@T("请输入")" autocomplete="off"
                            class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">@T("联系电话")</label>
                        <div class="layui-input-block">
                            <input type="tel" name="phone" lay-verify="required|phone" autocomplete="off" placeholder="@T("请输入")" lay-reqtext="@T("请填写联系方式")"
                            lay-affix="clear"  class="layui-input demo-phone">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">@T("邮箱")</label>
                        <div class="layui-input-inline">
                            <input type="text" name="email" lay-verify="required|email" placeholder="@T("请输入")" autocomplete="off"
                            class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">@T("所在区域")</label>
                        <div class="layui-input-inline">
                            <select name="quiz1" lay-verify="required" lay-filter="country" lay-search>
                                @foreach (Country item in Model.countrylist)
                                {
                                    <option value="@item.TwoLetterIsoCode">@item.Name</option>
                                }
                            </select>
                        </div>
                        <div class="layui-input-inline">
                            <select name="quiz2" lay-verify="" id="quiz2" lay-filter="province">
                            </select>
                        </div>
                        <div class="layui-input-inline">
                            <select name="quiz3" lay-verify="" id="quiz3">
                            </select>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">@T("详细地址")</label>
                        <div class="layui-input-block">
                            <input type="text" name="address" lay-verify="required" placeholder="@T("请输入")" autocomplete="off"
                                   class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">@T("申请主题")</label>
                        <div class="layui-input-block">
                            <input type="text" name="theme" lay-verify="required" placeholder="@T("请输入")" autocomplete="off"
                                   class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">@T("需求描述")</label>
                        <div class="layui-input-block">
                            <textarea id="myTextarea" name="remark" placeholder="@T("请输入")" lay-verify="required" class="layui-textarea"></textarea>
                        </div>
                    </div>
@*                     <div class="layui-form-item">
                        <label class="layui-form-label">验证码</label>
                        <div class="layui-input-inline">
                            <div class="layui-row">
                                <div class="layui-col-xs7">
                                    <div class="layui-input-wrap">
                                        <input type="text" name="captcha" value="" lay-verify="required" placeholder="" lay-reqtext="请填写验证码"
                                               autocomplete="off" class="layui-input" lay-affix="clear">
                                    </div>
                                </div>
                                <div class="layui-col-xs5">
                                    <div style="margin-left: 10px;">
                                        <img src="https://www.oschina.net/action/user/captcha"
                                             onclick="this.src='https://www.oschina.net/action/user/captcha?t='+ new Date().getTime();">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div> *@
                    <div class="layui-col-xs4">&nbsp;</div>
                    <div class="layui-col-xs4">
                        <div class="layui-form-item">
                            <button class="layui-btn layui-bg-blue" lay-submit lay-filter="demo1">@T("确认")</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>
<script>
    const data = [
        {  text: '@T("应用支持")', link: '#' },  // 标题
        {  text: '@T("焦点专题")', link: '@Url.Action("Index")' },
        {  text: '@T("资料下载")', link: '@Url.Action("DataDownload")' },
        {  text: '@T("应用视频")', link: '@Url.Action("AppVideo")' },
        {  text: '@T("常见问题")', link: '@Url.Action("CommonProblem")' },
        {  text: '@T("样品申请")', link: '@Url.Action("SampleApply")' },
        {  text: '@T("成品检测报告")', link: '@Url.Action("ProductInspectionReport")' }
    ];
    document.getElementById("myTextarea").value = "";

    document.addEventListener('DOMContentLoaded', function () {
      layui.use(['form', 'laydate', 'util'], function () {
        var form = layui.form;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var util = layui.util;

        // 添加型号按钮点击事件
        var addModelBtn = document.getElementById('addModel');
        if (addModelBtn) {
          addModelBtn.onclick = function () {
            console.log('添加型号按钮被点击');
            // 获取原始表单元素
            var original = document.getElementById('formElements');
            if (!original) {
              console.error('未找到formElements元素');
              return;
            }

            // 创建新的表单行
            var newRow = document.createElement('div');
            newRow.className = 'layui-inline';
            newRow.innerHTML = `
            <label class="layui-form-label">&nbsp;</label>
            <div class="layui-input-inline">
              <select name="productModel[]" lay-verify="required" lay-search>
                <option value="">@T("请选择")</option>
                @foreach(ProductClass item in Model.classlist)
                {
                    <option value="@item.Id">@item.Name</option>
                }
              </select>
            </div>
            <label class="layui-form-label">@T("数量")</label>
            <div class="layui-input-inline">
              <input type="number" name="price_min[]" lay-verify="required" placeholder="" autocomplete="off" class="layui-input" min="1" step="1" lay-affix="number">
            </div>
            <div class="layui-input-inline iconBox">
                  <div class="icon remove-row">
                    <i class="layui-icon layui-icon-subtraction"></i>
                  </div>
            </div>
          `;

            // 将新行插入到原始元素后面
            original.parentNode.insertBefore(newRow, original.nextSibling);

            // 重新渲染表单元素
            form.render();

            // 添加删除按钮事件
            var removeBtn = newRow.querySelector('.remove-row');
            if (removeBtn) {
              removeBtn.onclick = function () {
                newRow.remove();
                form.render();
              };
            }
          };
        } else {
          console.error('未找到addModel元素');
        }

        var provinceSelect = document.querySelector('select[name="quiz2"]').parentNode;
        var citySelect = document.querySelector('select[name="quiz3"]').parentNode;

        provinceSelect.style.display = 'none';
        citySelect.style.display = 'none';

        // 监听国家选择变化
        form.on('select(country)', function (data) {
          var countryValue = data.value;
          // console.log('countryValue :>> ', countryValue);


          // 如果选择的是中国或China，显示省市选择框，否则隐藏
          if (countryValue === 'CN') {
            provinceSelect.style.display = 'inline-block';
            citySelect.style.display = 'inline-block';

            $.getJSON('@Url.Action("QueryAllProvince")',{code:countryValue},function(res){
                if(res.success){
                    $("#quiz2").empty();
                    $("#quiz3").empty();
                    $("#quiz2").append('<option value="0">@selectProvinceText</option>');
                    $("#quiz3").append('<option value="0">@selectCityText</option>');
                    for(var i = 0; i < res.data.length; i++) {
                        $("#quiz2").append('<option value="'+res.data[i].AreaCode+'">'+res.data[i].Name+'</option>');
                    }
                    layui.form.render("select");
                }else{
                    layui.layer.msg(res.msg);
                }
            })
          } else {
            provinceSelect.style.display = 'none';
            citySelect.style.display = 'none';
          }
        });

        // 监听省份选择变化
         form.on('select(province)', function (data) {
            var provinceValue = data.value;
            $.getJSON('@Url.Action("QueryAllCity")',{code:provinceValue},function(res){
                if(res.success){
                    $("#quiz3").empty();
                    $("#quiz3").append('<option value="0">@selectCityText</option>');
                    for(var i = 0; i < res.data.length; i++) {
                        $("#quiz3").append('<option value="'+res.data[i].AreaCode+'">'+res.data[i].Name+'</option>');
                    }
                    layui.form.render("select");
                }else{
                    layui.layer.msg(res.msg);
                }
            })
         });

        // 提交事件
        form.on('submit(demo1)', function (data) {
          var field = data.field; // 获取表单字段值
          // 显示填写结果
          console.log('field :>> ', field);

          // 此处可执行 Ajax 等操作
          // …

          $.post('@Url.Action("AddSampleApply")',field,function(res){
            layui.layer.msg(res.msg);
            if(res.success){
                setTimeout(function(){
                    window.location.reload();
                },2000)
            }
          })

          return false; // 阻止默认 form 跳转
        });

        // 日期
        // laydate.render({
        //   elem: '#date'
        // });
      });
    });
</script>